use super::*;
use googletest::{assert_that, prelude::*};
use std::os::unix::net::UnixDatagram;
use std::{env, time::Duration};
use tokio::time::{timeout, Instant};

fn setup_notify_socket(path: &str) {
    env::set_var(NOTIFY_SOCKET_NAME, path);
}

fn cleanup_notify_socket() {
    env::remove_var(NOTIFY_SOCKET_NAME);
}

#[test]
fn le_watchdog_est_cree_avec_l_intervalle_correct() {
    let interval = Duration::from_secs(5);
    let watchdog = SystemdWatchdog::new_for_test(interval);

    assert_that!(watchdog.interval, eq(interval));
}

#[test]
fn tous_les_types_de_messages_fonctionnent_sans_socket() {
    // Ensure all message types work when no socket is configured
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new_for_test(Duration::from_secs(1));

    // Test READY_MESSAGE
    let result = watchdog.send_notification(READY_MESSAGE);
    assert_that!(result, ok(anything()));

    // Test WATCHDOG_MESSAGE
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);
    assert_that!(result, ok(anything()));

    // Test custom message
    let result = watchdog.send_notification("STATUS=Running");
    assert_that!(result, ok(anything()));
}

#[test]
fn l_envoi_de_notification_fonctionne_avec_un_socket_unix_reel() {
    // Use a temporary socket path in /tmp
    let socket_path = "/tmp/test_watchdog_socket";

    // Remove any existing socket file
    let _ = std::fs::remove_file(socket_path);

    // Create a Unix socket to receive messages
    let receiver = match UnixDatagram::bind(socket_path) {
        Ok(socket) => socket,
        Err(_) => {
            // Skip test if we can't create socket
            return;
        }
    };

    setup_notify_socket(socket_path);

    let watchdog = SystemdWatchdog::new_for_test(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    assert_that!(result, ok(anything()));

    // Try to receive the message
    let mut buffer = [0u8; 1024];
    let received = receiver.recv(&mut buffer);

    match received {
        Ok(size) => {
            assert_that!(&buffer[..size], eq(WATCHDOG_MESSAGE.as_bytes()));
        }
        Err(_) => {
            // Socket might not receive immediately, but send should succeed
        }
    }

    cleanup_notify_socket();
    let _ = std::fs::remove_file(socket_path);
}

#[test]
fn l_envoi_de_notification_echoue_avec_un_socket_sans_permission() {
    // Try to use a path that would cause permission denied
    setup_notify_socket("/root/restricted_socket");

    let watchdog = SystemdWatchdog::new_for_test(Duration::from_secs(1));
    let result = watchdog.send_notification(WATCHDOG_MESSAGE);

    // Should fail due to permission issues
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[tokio::test]
async fn le_watchdog_continue_malgre_les_echecs_de_heartbeat() {
    // Set up a socket path that will cause failures
    setup_notify_socket("/tmp/non_existent_socket_for_failure_test");

    let watchdog = SystemdWatchdog::new_for_test(Duration::from_millis(50));

    // Start the watchdog and let it run for enough time to attempt multiple heartbeats
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(200), start_future).await;

    // Should timeout because start() continues running even with failures
    assert_that!(result, err(anything()));

    cleanup_notify_socket();
}

#[tokio::test]
async fn le_timing_du_watchdog_est_precis() {
    cleanup_notify_socket();

    let interval = Duration::from_millis(100);
    let watchdog = SystemdWatchdog::new_for_test(interval);

    let start_time = Instant::now();
    let start_future = watchdog.start();

    // Let it run for approximately 2.5 intervals
    let result = timeout(Duration::from_millis(250), start_future).await;
    let elapsed = start_time.elapsed();

    // Should timeout and elapsed time should be close to 250ms with some tolerance
    assert_that!(result, err(anything()));
    assert_that!(elapsed.as_millis(), ge(240));
    assert_that!(elapsed.as_millis(), le(260));
}

// Tests for TaskHealthMonitor
#[tokio::test]
async fn le_health_monitor_est_cree_vide() {
    let monitor = TaskHealthMonitor::new();
    let health_status = monitor.get_health_status().await;
    assert_that!(health_status, empty());
}

#[tokio::test]
async fn une_tache_peut_etre_enregistree_et_envoyer_des_heartbeats() {
    let monitor = TaskHealthMonitor::new();
    let task_id = TaskId::new("test_task");

    // Register a task
    monitor
        .register_task(task_id.clone(), Duration::from_secs(5))
        .await;

    // Check initial health status
    let health_status = monitor.get_health_status().await;
    assert_that!(health_status, len(eq(1)));
    assert_that!(&health_status[0].0, eq(&task_id));
    assert_that!(health_status[0].1, eq(true)); // Should be healthy initially

    // Send heartbeat
    monitor.heartbeat(&task_id).await;

    // Should still be healthy
    let health_status = monitor.get_health_status().await;
    assert_that!(health_status[0].1, eq(true));
}

#[tokio::test]
async fn une_tache_echoue_apres_le_timeout() {
    let monitor = TaskHealthMonitor::new();
    let task_id = TaskId::new("test_task");

    // Register a task with very short timeout
    monitor
        .register_task(task_id.clone(), Duration::from_millis(50))
        .await;

    // Initially healthy
    let health_status = monitor.get_health_status().await;
    assert_that!(health_status[0].1, eq(true));

    // Wait for timeout
    tokio::time::sleep(Duration::from_millis(100)).await;

    // Should now be unhealthy
    let health_status = monitor.get_health_status().await;
    assert_that!(health_status[0].1, eq(false));
}

#[tokio::test]
async fn une_tache_peut_etre_desenregistree() {
    let monitor = TaskHealthMonitor::new();
    let task_id = TaskId::new("test_task");

    // Register and then unregister
    monitor
        .register_task(task_id.clone(), Duration::from_secs(5))
        .await;
    assert_that!(monitor.get_health_status().await, len(eq(1)));

    monitor.unregister_task(&task_id).await;
    assert_that!(monitor.get_health_status().await, empty());
}

#[tokio::test]
async fn un_heartbeat_pour_une_tache_non_enregistree_ne_cause_pas_d_erreur() {
    let monitor = TaskHealthMonitor::new();
    let task_id = TaskId::new("unregistered_task");

    // Should not panic when sending heartbeat for unregistered task
    monitor.heartbeat(&task_id).await;

    // Should still have no registered tasks
    assert_that!(monitor.get_health_status().await, empty());
}

#[tokio::test]
async fn le_watchdog_fonctionne_avec_le_health_monitor() {
    cleanup_notify_socket();

    let health_monitor = TaskHealthMonitor::new();
    let task_id = TaskId::new("test_task");

    // Register a healthy task
    health_monitor
        .register_task(task_id.clone(), Duration::from_secs(10))
        .await;

    let watchdog = SystemdWatchdog::new_for_test_with_health_monitor(
        Duration::from_millis(100),
        health_monitor.clone(),
    );

    // Watchdog should have the health monitor
    assert_that!(watchdog.get_health_monitor(), some(anything()));

    // Start watchdog in background and let it run briefly
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(250), start_future).await;

    // Should timeout (watchdog runs indefinitely)
    assert_that!(result, err(anything()));
}

#[tokio::test]
async fn le_watchdog_fonctionne_sans_health_monitor() {
    cleanup_notify_socket();

    let watchdog = SystemdWatchdog::new_for_test(Duration::from_millis(100));

    // Should not have health monitor
    assert_that!(watchdog.get_health_monitor(), none());

    // Should still work normally
    let start_future = watchdog.start();
    let result = timeout(Duration::from_millis(250), start_future).await;

    // Should timeout (watchdog runs indefinitely)
    assert_that!(result, err(anything()));
}

#[tokio::test]
async fn plusieurs_instances_de_watchdog_peuvent_fonctionner_simultanement() {
    cleanup_notify_socket();

    let watchdog1 = SystemdWatchdog::new_for_test(Duration::from_millis(50));
    let watchdog2 = SystemdWatchdog::new_for_test(Duration::from_millis(75));

    // Start both watchdogs concurrently
    let future1 = watchdog1.start();
    let future2 = watchdog2.start();

    let result = timeout(Duration::from_millis(200), async {
        tokio::select! {
            _ = future1 => {},
            _ = future2 => {},
        }
    })
    .await;

    // Should timeout because both start() methods run indefinitely
    assert_that!(result, err(anything()));
}
